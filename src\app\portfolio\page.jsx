import React from "react";
import Tab from "@/Components/Portfolio/Tab";
import WebApp from "@/Components/Portfolio/Web_Dev";
export const metadata = {
  title: "Discover our portfolio to have a glimpse into our projects.",
  description: "Explore our portfolio to see a collection of our unique projects that demonstrate our dedication to quality and inventive problem-solving in the software industry.",
};
const WebAppPortfolio = () => {
  return (
    <section className=" w-[90%] mx-auto mb-10 md:mb-24">
      <div className="max-w-[85vw] mx-auto py-12">
        <div className="w-[70vw] mx-auto">
          <h1 className="text-4xl text-center text-[#F245A1] font-semibold">
           Explore Our Portfolio
          </h1>
          <p className="text-center text-lg font-normal mt-10">
            Discover the breadth and depth of our innovative projects at
            Valueans. Dive into a showcase of our diverse software solutions,
            each crafted with expertise and tailored to meet unique client
            needs.
          </p>
        </div>
        {/* <div className="max-w-fit mx-auto my-14">
          <Tab />
        </div> */}
      </div>
      <WebApp />
    </section>
  );
};

export default WebAppPortfolio;
