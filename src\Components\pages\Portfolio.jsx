"use client";
import React, { useState } from "react";
import PortfolioCard from "../Card/PortfolioCards";

const Portfolio = ({ title, highlightedText, description }) => {
  const [currentSlide, setCurrentSlide] = useState(0);

  const portfolioData = [
    {
      navLink:"/portfolio/somatic-fitness-app",
      imageSrc: "/Images/somatic.png",
      altText: "Somatic",
      title: "Somatic Fitness App",
      description:
        "Experience the ultimate fitness journey with the Somatic Fitness Corporate app, a cutting-edge mobile solution designed exclusively for our client's …",
    },
    {
      navLink:"/portfolio/enbarr",
      imageSrc: "/Images/Enbarr.png",
      altText: "Enbarr",
      title: "Enbarr",
      description:
        "Enbarr is designed for the horse community where horse enthusiasts can easily buy and sell horses online. This platform provides a user-friendly …",
    },
    {
      navLink:"/portfolio/paintready",
      imageSrc: "/Images/paint-ready.png",
      altText: "Paint Ready",
      title: "Paint Ready",
      description:
        "Cutting Edge Painting LLC stands out in the industry with our collaborative success, marked by the development of Paint Ready, a custom software …",
    },
  ];

  // Track active slide index
  const handleScroll = (e) => {
    const slideIndex = Math.round(e.target.scrollLeft / e.target.clientWidth);
    setCurrentSlide(slideIndex);
  };

  return (
    <section className="container">
      <div className="w-full md:max-w-[50%] mx-auto mb-10">
        <h2 className="text-center font-bold text-2xl md:text-2xl">
          {title} <span className="text-[#7716BC]">{highlightedText}</span>
        </h2>
        <p className="text-center text-base md:text-xl py-1 md:py-3">
          {description}
        </p>
      </div>

      {/* Carousel for smaller screens */}
      <div
        className="flex gap-5 overflow-x-scroll md:hidden snap-x snap-mandatory scrollbar-hide"
        onScroll={handleScroll}
      >
        {portfolioData.map((item, index) => (
          <div
            key={index}
            className="flex-shrink-0 w-full snap-center px-4 sm:w-[85%] mx-auto"
          >
            <PortfolioCard
              title={item.title}
              description={item.description}
              imageSrc={item.imageSrc}
              altText={item.altText}
              navLink={item.navLink}
            />
          </div>
        ))}
      </div>

      {/* Carousel Dots */}
      <div className="flex justify-center mt-4 md:hidden">
        {portfolioData.map((_, index) => (
          <div
            key={index}
            className={`w-3 h-3 mx-1 rounded-full ${
              currentSlide === index ? "bg-[#7716BC]" : "bg-gray-300"
            }`}
          ></div>
        ))}
      </div>

      {/* Grid for larger screens */}
      <div className="hidden md:grid grid-cols-2 gap-6  md:grid-cols-3">
        {portfolioData.map((item, index) => (
          <PortfolioCard
            key={index}
            title={item.title}
            description={item.description}
            imageSrc={item.imageSrc}
            altText={item.altText}
            navLink={item.navLink}
          />
        ))}
      </div>
    </section>
  );
};

export default Portfolio;
