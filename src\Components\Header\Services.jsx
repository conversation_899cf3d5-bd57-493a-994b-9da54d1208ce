"use client";
import React from "react";
import Link from "next/link";

const Services = ({ onNavigate }) => {
  // Enhanced navigation handler for dropdown items
  const handleEnhancedNavigation = (href, event) => {
    // Allow right-click (context menu) - don't prevent default
    if (event.button === 2) {
      return;
    }

    // Allow Ctrl+click (or Cmd+click on Mac) to open in new tab
    if (event.ctrlKey || event.metaKey) {
      window.open(href, '_blank');
      return;
    }

    // For normal left-click, prevent default and use custom navigation
    event.preventDefault();
    onNavigate(href);
  };
  return (
    <div className="container mx-auto p-10 border rounded-lg">
      <div className="grid grid-cols-6 gap-4 ">
        <div className="col-span-1">
          <div className="flex flex-col gap-2">
            <h4 className="text-base font-bold"> Custom Software Solutions</h4>
            <Link
              href="/custom-software-development"
              onClick={(e) => handleEnhancedNavigation("/custom-software-development", e)}
              onMouseDown={(e) => handleEnhancedNavigation("/custom-software-development", e)}
              className="text-sm text-gray-900 hover:text-[#F245A1] hover:underline cursor-pointer"
            >
              Software Development
            </Link>
            <Link
              href="/custom-website-development"
              onClick={(e) => handleEnhancedNavigation("/custom-website-development", e)}
              onMouseDown={(e) => handleEnhancedNavigation("/custom-website-development", e)}
              className="text-sm text-gray-900 hover:text-[#F245A1] hover:underline cursor-pointer"
            >
              Custom Web Development
            </Link>
            <Link
              href="/saas-app-development"
              onClick={(e) => handleEnhancedNavigation("/saas-app-development", e)}
              onMouseDown={(e) => handleEnhancedNavigation("/saas-app-development", e)}
              className="text-sm text-gray-900 hover:text-[#F245A1] hover:underline cursor-pointer"
            >
              Saas Development
            </Link>
            <Link
              href="/Full_Stack_Development_Services"
              onClick={(e) => handleEnhancedNavigation("/Full_Stack_Development_Services", e)}
              onMouseDown={(e) => handleEnhancedNavigation("/Full_Stack_Development_Services", e)}
              className="text-sm text-gray-900 hover:text-[#F245A1] hover:underline cursor-pointer"
            >
              Full Stack Development
            </Link>
            <div
              onClick={() => onNavigate("/mobile-app-development")}
              className="text-sm text-gray-900 hover:text-[#F245A1] hover:underline cursor-pointer"
            >
              Mobile App Development
            </div>
            <div
              onClick={() => onNavigate("/financial-app-development")}
              className="text-sm text-gray-900 hover:text-[#F245A1] hover:underline cursor-pointer"
            >
              Fintech
            </div>
            <div
              onClick={() => onNavigate("/health-care-development")}
              className="text-sm text-gray-900 hover:text-[#F245A1] hover:underline cursor-pointer"
            >
              Healthcare Development
            </div>
          </div>
        </div>
        <div className="col-span-1">
          <div className="flex flex-col gap-2">
            <h4 className="text-base font-bold">IT Consulting</h4>
            <div
              onClick={() => onNavigate("/AI")}
              className="text-sm text-gray-900 hover:text-[#F245A1] hover:underline cursor-pointer"
            >
              AI
            </div>
            <div
              onClick={() => onNavigate("/App_Integration")}
              className="text-sm text-gray-900 hover:text-[#F245A1] hover:underline cursor-pointer"
            >
              Application Integration
            </div>
            <div
              onClick={() => onNavigate("/Cloud_Services")}
              className="text-sm text-gray-900 hover:text-[#F245A1] hover:underline cursor-pointer"
            >
              Cloud Services
            </div>
            <div
              onClick={() => onNavigate("/Business_Intelligence")}
              className="text-sm text-gray-900 hover:text-[#F245A1] hover:underline cursor-pointer"
            >
              Business Intelligence
            </div>
            <div
              onClick={() => onNavigate("/product-management")}
              className="text-sm text-gray-900 hover:text-[#F245A1] hover:underline cursor-pointer"
            >
              Product Management
            </div>
            <div
              onClick={() => onNavigate("/ML")}
              className="text-sm text-gray-900 hover:text-[#F245A1] hover:underline cursor-pointer"
            >
              ML
            </div>
          </div>
        </div>
        <div className="col-span-1">
          <div className="flex flex-col gap-2">
            <h4 className="text-base font-bold">Testing & QA</h4>
            <div
              onClick={() => onNavigate("/Quality_Assurance")}
              className="text-sm text-gray-900 hover:text-[#F245A1] hover:underline cursor-pointer"
            >
              Quality Assurance & Testing
            </div>
          </div>
        </div>
        <div className="col-span-1">
          <div className="flex flex-col gap-2">
            <h4 className="text-base font-bold">Data Solutions</h4>
            <div
              onClick={() => onNavigate("/Data_and_Analytics")}
              className="text-sm text-gray-900 hover:text-[#F245A1] hover:underline cursor-pointer"
            >
              Data Analytics
            </div>
            <div
              onClick={() => onNavigate("/DataEngineering")}
              className="text-sm text-gray-900 hover:text-[#F245A1] hover:underline cursor-pointer"
            >
              Data Engineering
            </div>
          </div>
        </div>
        <div className="col-span-1">
          <div className="flex flex-col gap-2">
            <h4 className="text-base font-bold">Design Services</h4>
            <div
              onClick={() => onNavigate("/ui-ux")}
              className="text-sm text-gray-900 hover:text-[#F245A1] hover:underline cursor-pointer"
            >
              UI UX designing
            </div>
          </div>
        </div>
        <div className="col-span-1">
          <div className="flex flex-col gap-2">
            <h4 className="text-base font-bold">Application Services</h4>
            <div
              onClick={() => onNavigate("/Maintenance_and_Support")}
              className="text-sm text-gray-900 hover:text-[#F245A1] hover:underline cursor-pointer"
            >
              Maintainence and Support
            </div>
            <div
              onClick={() => onNavigate("/Dedicated_Deployment_teams")}
              className="text-sm text-gray-900 hover:text-[#F245A1] hover:underline cursor-pointer"
            >
              Dedicated Deployment Teams
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Services;
