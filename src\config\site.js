// Site Configuration
// Update this file when moving from Vercel to production domain

export const siteConfig = {
  // Current Vercel URL - will be changed to valueans.com in production
  url: "https://kapoorsoftwaresolutions-git-master-valueans-projects.vercel.app",
  
  // Production URL (for future use)
  productionUrl: "https://valueans.com",
  
  // Site information
  name: "Kapoor Software Solutions",
  description: "Kapoor Software Solutions provides custom software development, mobile app development, web development, AI/ML solutions, and digital transformation services.",
  
  // Social links
  social: {
    twitter: "https://twitter.com/kapoorsoftware",
    linkedin: "https://www.linkedin.com/company/kapoor-software-solutions",
  },
  
  // SEO settings
  seo: {
    defaultTitle: "Kapoor Software Solutions - Custom Software Development Services",
    titleTemplate: "%s | Kapoor Software Solutions",
    defaultDescription: "Kapoor Software Solutions provides custom software development, mobile app development, web development, AI/ML solutions, and digital transformation services.",
    keywords: [
      "Custom Software Development",
      "Mobile App Development", 
      "Web Development",
      "AI/ML Solutions",
      "Digital Transformation",
      "Software Solutions",
      "Technology Services",
      "Kapoor Software Solutions",
    ],
  },
};

// Helper function to get the current base URL
export const getBaseUrl = () => {
  return siteConfig.url;
};

// Helper function to get full URL for a path
export const getFullUrl = (path = "") => {
  const baseUrl = getBaseUrl();
  return `${baseUrl}${path.startsWith('/') ? path : `/${path}`}`;
};

// Helper function for when moving to production
export const switchToProduction = () => {
  // This function can be used to easily switch all URLs to production
  // Just update siteConfig.url to siteConfig.productionUrl
  console.log("To switch to production, update siteConfig.url to:", siteConfig.productionUrl);
};
